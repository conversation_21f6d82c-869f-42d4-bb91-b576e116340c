/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s;
}

a:hover {
    color: #2980b9;
}

img {
    max-width: 100%;
}

/* 头部样式 */
header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    color: #555;
    font-weight: 500;
    padding: 10px 0;
    position: relative;
}

nav ul li a:hover {
    color: #3498db;
}

nav ul li a.active {
    color: #3498db;
}

nav ul li a.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #3498db;
}

/* 英雄区域样式 */
.hero {
    background-color: #3498db;
    color: #fff;
    padding: 80px 0;
    text-align: center;
    margin-bottom: 50px;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
}

/* 主要内容区域样式 */
main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    margin-bottom: 50px;
}

.section-title {
    font-size: 1.8rem;
    margin-bottom: 30px;
    color: #2c3e50;
    position: relative;
    padding-bottom: 10px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #3498db;
}

/* 文章卡片样式 */
.post-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-bottom: 30px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.post-date {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.post-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.post-title a {
    color: #2c3e50;
}

.post-title a:hover {
    color: #3498db;
}

.post-excerpt {
    color: #555;
    margin-bottom: 20px;
}

.read-more {
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}

.read-more i {
    margin-left: 5px;
    transition: transform 0.3s;
}

.read-more:hover i {
    transform: translateX(5px);
}

/* 侧边栏样式 */
.sidebar {
    position: sticky;
    top: 100px;
}

.widget {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin-bottom: 30px;
}

.widget h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: #2c3e50;
    position: relative;
    padding-bottom: 10px;
}

.widget h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #3498db;
}

.about-me {
    text-align: center;
}

.avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin-bottom: 15px;
    object-fit: cover;
}

.btn {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    padding: 8px 20px;
    border-radius: 4px;
    margin-top: 10px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
    color: #fff;
}

.categories ul {
    list-style: none;
}

.categories li {
    margin-bottom: 10px;
}

.categories a {
    display: flex;
    justify-content: space-between;
    color: #555;
}

.categories a:hover {
    color: #3498db;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f1f2f6;
    border-radius: 50%;
    color: #555;
    transition: background-color 0.3s, color 0.3s;
}

.social-icons a:hover {
    background-color: #3498db;
    color: #fff;
}

/* 页脚样式 */
footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 30px 0;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        position: static;
    }
    
    header .container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 20px;
    }
    
    nav ul li {
        margin-left: 15px;
        margin-right: 15px;
    }
}

/* 文章页面样式 */
.post-page {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 40px;
    margin-bottom: 50px;
}

.post-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.post-header .post-date {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.post-header .post-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #2c3e50;
}

.post-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.post-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.post-content p,
.post-content ul,
.post-content ol,
.post-content blockquote {
    margin-bottom: 25px;
}

.post-content h2 {
    font-size: 1.8rem;
    margin: 40px 0 20px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.post-content h3 {
    font-size: 1.5rem;
    margin: 30px 0 15px;
    color: #2c3e50;
}

.post-content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 20px;
    color: #555;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
}

.post-content img {
    border-radius: 8px;
    margin: 30px 0;
}

.post-content ul, .post-content ol {
    padding-left: 30px;
}

.post-content li {
    margin-bottom: 8px;
}

.post-footer {
    margin-top: 50px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.post-tag {
    background-color: #f1f2f6;
    color: #555;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: background-color 0.3s, color 0.3s;
}

.post-tag:hover {
    background-color: #3498db;
    color: #fff;
}

.post-navigation {
    display: flex;
    justify-content: space-between;
}

.post-navigation a {
    display: flex;
    align-items: center;
    color: #555;
}

.post-navigation a:hover {
    color: #3498db;
}

.post-navigation .prev i {
    margin-right: 10px;
}

.post-navigation .next i {
    margin-left: 10px;
}

/* 关于页面样式 */
.about-page {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 40px;
}

.about-header {
    text-align: center;
    margin-bottom: 40px;
}

.about-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin-bottom: 20px;
    object-fit: cover;
}

.about-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.about-content p {
    margin-bottom: 25px;
}

.about-content h2 {
    font-size: 1.8rem;
    margin: 40px 0 20px;
    color: #2c3e50;
}

.skills {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.skill-tag {
    background-color: #f1f2f6;
    color: #555;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
}

/* 关于页面额外样式 */
.about-subtitle {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 0;
}

.about-content ul {
    padding-left: 20px;
}

.about-content ul li {
    margin-bottom: 10px;
}

.contact-info {
    margin: 20px 0;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.contact-item i {
    width: 30px;
    color: #3498db;
    margin-right: 15px;
}

.about-footer {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}

.about-footer p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 20px;
}