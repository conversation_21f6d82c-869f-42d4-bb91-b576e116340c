document.addEventListener('DOMContentLoaded', function() {
    // 移动端导航菜单切换
    const setupMobileNav = () => {
        // 在小屏幕下添加汉堡菜单按钮（如果需要）
        const header = document.querySelector('header .container');
        const nav = document.querySelector('nav');
        
        if (window.innerWidth <= 768 && !document.querySelector('.mobile-menu-toggle')) {
            const mobileMenuToggle = document.createElement('button');
            mobileMenuToggle.className = 'mobile-menu-toggle';
            mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            header.insertBefore(mobileMenuToggle, nav);
            
            mobileMenuToggle.addEventListener('click', function() {
                nav.classList.toggle('active');
                this.innerHTML = nav.classList.contains('active') 
                    ? '<i class="fas fa-times"></i>' 
                    : '<i class="fas fa-bars"></i>';
            });
        }
    };
    
    // 初始化设置
    setupMobileNav();
    
    // 窗口大小改变时重新设置
    window.addEventListener('resize', setupMobileNav);
    
    // 平滑滚动到顶部
    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };
    
    // 创建回到顶部按钮
    const createScrollTopButton = () => {
        const scrollTopBtn = document.createElement('button');
        scrollTopBtn.className = 'scroll-top-btn';
        scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        document.body.appendChild(scrollTopBtn);
        
        // 显示/隐藏回到顶部按钮
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollTopBtn.classList.add('show');
            } else {
                scrollTopBtn.classList.remove('show');
            }
        });
        
        // 点击回到顶部
        scrollTopBtn.addEventListener('click', scrollToTop);
    };
    
    createScrollTopButton();
    
    // 为文章添加动画效果
    const animatePostCards = () => {
        const postCards = document.querySelectorAll('.post-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });
        
        postCards.forEach(card => {
            observer.observe(card);
        });
    };
    
    // 如果浏览器支持 IntersectionObserver，则添加动画效果
    if ('IntersectionObserver' in window) {
        animatePostCards();
    }
    
    // 添加暗黑模式切换功能
    const setupDarkMode = () => {
        // 创建暗黑模式切换按钮
        const darkModeToggle = document.createElement('button');
        darkModeToggle.className = 'dark-mode-toggle';
        darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        document.querySelector('header .container').appendChild(darkModeToggle);
        
        // 检查用户偏好
        const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const savedTheme = localStorage.getItem('theme');
        
        // 根据用户偏好或保存的主题设置初始主题
        if (savedTheme === 'dark' || (savedTheme !== 'light' && prefersDarkMode)) {
            document.body.classList.add('dark-mode');
            darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
        
        // 切换暗黑模式
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                localStorage.setItem('theme', 'dark');
                this.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                localStorage.setItem('theme', 'light');
                this.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    };
    
    setupDarkMode();
    
    // 添加搜索功能
    const setupSearch = () => {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'search-container';
        
        const searchIcon = document.createElement('button');
        searchIcon.className = 'search-icon';
        searchIcon.innerHTML = '<i class="fas fa-search"></i>';
        
        const searchForm = document.createElement('form');
        searchForm.className = 'search-form';
        searchForm.innerHTML = `
            <input type="text" placeholder="搜索文章..." class="search-input">
            <button type="submit" class="search-submit"><i class="fas fa-search"></i></button>
        `;
        
        searchContainer.appendChild(searchIcon);
        searchContainer.appendChild(searchForm);
        
        document.querySelector('header .container').insertBefore(
            searchContainer,
            document.querySelector('nav')
        );
        
        // 切换搜索表单显示
        searchIcon.addEventListener('click', function() {
            searchForm.classList.toggle('active');
            if (searchForm.classList.contains('active')) {
                searchForm.querySelector('input').focus();
            }
        });
        
        // 处理搜索提交
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = this.querySelector('input').value.trim();
            
            if (searchTerm) {
                // 这里可以实现实际的搜索功能
                // 简单示例：跳转到带有搜索参数的页面
                alert(`搜索: ${searchTerm}\n实际项目中可以实现搜索功能或跳转到搜索结果页面`);
            }
        });
    };
    
    setupSearch();
    
    // 添加CSS样式
    const addStyles = () => {
        const style = document.createElement('style');
        style.textContent = `
            .mobile-menu-toggle {
                display: none;
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #555;
            }
            
            .scroll-top-btn {
                position: fixed;
                bottom: 30px;
                right: 30px;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background-color: #3498db;
                color: #fff;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s, visibility 0.3s;
                z-index: 99;
            }
            
            .scroll-top-btn.show {
                opacity: 1;
                visibility: visible;
            }
            
            .post-card {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.6s, transform 0.6s;
            }
            
            .post-card.animate {
                opacity: 1;
                transform: translateY(0);
            }
            
            .dark-mode-toggle {
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: #555;
                margin-left: 20px;
            }
            
            .dark-mode {
                background-color: #1a1a2e;
                color: #e6e6e6;
            }
            
            .dark-mode header {
                background-color: #16213e;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            }
            
            .dark-mode .logo {
                color: #e6e6e6;
            }
            
            .dark-mode nav ul li a {
                color: #b8b8b8;
            }
            
            .dark-mode .post-card,
            .dark-mode .widget {
                background-color: #16213e;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }
            
            .dark-mode .post-title a {
                color: #e6e6e6;
            }
            
            .dark-mode .post-excerpt,
            .dark-mode .categories a {
                color: #b8b8b8;
            }
            
            .dark-mode .section-title,
            .dark-mode .widget h3 {
                color: #e6e6e6;
            }
            
            .dark-mode .social-icons a {
                background-color: #1a1a2e;
                color: #b8b8b8;
            }
            
            .dark-mode .dark-mode-toggle {
                color: #e6e6e6;
            }
            
            .search-container {
                display: flex;
                align-items: center;
                margin-left: auto;
                margin-right: 20px;
            }
            
            .search-icon {
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: #555;
            }
            
            .dark-mode .search-icon {
                color: #e6e6e6;
            }
            
            .search-form {
                display: none;
                position: relative;
            }
            
            .search-form.active {
                display: flex;
            }
            
            .search-input {
                padding: 8px 15px;
                border: 1px solid #ddd;
                border-radius: 20px;
                font-size: 0.9rem;
                width: 200px;
            }
            
            .dark-mode .search-input {
                background-color: #1a1a2e;
                border-color: #444;
                color: #e6e6e6;
            }
            
            .search-submit {
                background: none;
                border: none;
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
                color: #555;
            }
            
            .dark-mode .search-submit {
                color: #e6e6e6;
            }
            
            @media (max-width: 768px) {
                .mobile-menu-toggle {
                    display: block;
                }
                
                nav {
                    display: none;
                }
                
                nav.active {
                    display: block;
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background-color: #fff;
                    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
                    padding: 20px;
                }
                
                .dark-mode nav.active {
                    background-color: #16213e;
                }
                
                nav ul {
                    flex-direction: column;
                }
                
                nav ul li {
                    margin: 10px 0;
                }
                
                .search-container {
                    margin-left: 0;
                    margin-right: auto;
                }
            }
        `;
        document.head.appendChild(style);
    };
    
    addStyles();
});